'use client';
import { useRouter } from 'next/navigation';
import { PageForm } from '@/modules/page/components/create-page-form';
import { useGetPage } from '@/modules/page/queries/use-get-page';
import { useUpdatePage } from '@/modules/page/mutations/use-update-page';
import { AboutUsForm } from './components/about-us-form';

export default function AboutUsEditPage({ id }: { id: string }) {
  const router = useRouter();
  const { data: pageResponse, isLoading } = useGetPage(id);
  const updatePage = useUpdatePage();

  const page = pageResponse?.data;

  const handleSubmit = async (data: any) => {
    try {
      await updatePage.mutateAsync({ id: id, data });
      router.push('/pages');
    } catch (error) {
      console.error('Failed to update page:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading page...</div>
        </div>
      </div>
    );
  }

  if (!page) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">Page not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <AboutUsForm
        page={page}
        onSubmit={handleSubmit}
        loading={updatePage.isPending}
      />
    </div>
  );
}
