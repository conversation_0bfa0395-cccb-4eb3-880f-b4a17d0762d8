import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function useDeleteBlog() {
  const queryClient = useQueryClient();

  return useMutation<IApiResponse<null>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/blogs/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
    },
  });
}
