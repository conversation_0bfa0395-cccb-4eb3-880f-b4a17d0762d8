import { IProduct } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetProduct(page = 1, perPage = 10) {
  return useQuery<IApiResponse<IProduct[]>, Error>({
    queryKey: ['product', page, perPage],
    queryFn: () =>
      fetch(`/api/product?page=${page}&perPage=${perPage}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
