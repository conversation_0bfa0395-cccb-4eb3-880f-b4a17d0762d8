export interface IProduct {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string[];
  subCategoryId: string;
  subCategory?: {
    id: string;
    name: string;
  };
  showOnNav: boolean;
}

export interface IProductTab {
  id: string;
  name: string;
  productId: string;
  product?: {
    id: string;
    name: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface IProductTabItem {
  id: string;
  title: string;
  description: string;
  productTabId: string;
  productTab?: IProductTab;
  createdAt?: string;
}
