import { ISubCategory } from '@/types/sub-category';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetSubCategory() {
  return useQuery<IApiResponse<ISubCategory[]>, Error>({
    queryKey: ['sub-category'],
    queryFn: () =>
      fetch(`/api/sub-category`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
