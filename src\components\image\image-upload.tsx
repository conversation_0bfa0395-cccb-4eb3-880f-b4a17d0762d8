'use client';

import React, { useRef, useState, useEffect } from 'react';

interface FileUploadProps {
  value?: string[] | string | null;
  onChange?: (urls: string[] | string | null) => void;
  accept?: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  multiple?: boolean;
}

interface UploadResponse {
  statusCode: number;
  success: boolean;
  data:
    | {
        url: string;
        id: string;
      }
    | Array<{
        url: string;
        status: string;
        name: string;
      }>;
  path: string;
  message: string;
  meta: object;
}

const FileUpload: React.FC<FileUploadProps> = ({
  value = null,
  onChange,
  accept = 'image/*,video/*,.pdf',
  label = 'Upload Files',
  disabled = false,
  className = '',
  multiple = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    if (value) {
      const urls = Array.isArray(value) ? value : [value];
      setPreviewUrls(urls);
    } else {
      setPreviewUrls([]);
    }
  }, [value]);

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return '🎥';
      default:
        return '🖼️';
    }
  };

  const uploadFiles = async (files: FileList) => {
    setUploading(true);

    try {
      const formData = new FormData();

      if (multiple) {
        // For multiple files, use /api/image/uploads
        Array.from(files).forEach((file) => {
          formData.append('files', file);
        });

        const response = await fetch('/api/image/uploads', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error('Upload failed');
        }

        const result: UploadResponse = await response.json();

        if (result.success && Array.isArray(result.data)) {
          const newUrls = result.data.map((item) => item.url);
          // Append new URLs to existing ones
          const combinedUrls = [...previewUrls, ...newUrls];
          setPreviewUrls(combinedUrls);
          onChange?.(combinedUrls);
        }
      } else {
        // For single file, use /api/image/upload
        formData.append('file', files[0]);

        const response = await fetch('/api/image/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error('Upload failed');
        }

        const result: UploadResponse = await response.json();

        if (result.success && !Array.isArray(result.data)) {
          const url = result.data.url;
          setPreviewUrls([url]);
          onChange?.(url);
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      // You might want to show an error message to the user
    } finally {
      setUploading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      return;
    }

    uploadFiles(files);
    e.target.value = '';
  };

  const handleClearFile = (index: number) => {
    const newUrls = previewUrls.filter((_, i) => i !== index);
    setPreviewUrls(newUrls);

    if (multiple) {
      onChange?.(newUrls.length > 0 ? newUrls : null);
    } else {
      onChange?.(null);
    }
  };

  const handleClearAll = () => {
    setPreviewUrls([]);
    onChange?.(null);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const renderPreview = (url: string, index: number) => {
    const fileName = url.split('/').pop() || 'file';
    const isImage = url.match(/\.(jpg|jpeg|png|gif|webp)$/i);
    const isVideo = url.match(/\.(mp4|avi|mov|wmv|flv|webm)$/i);
    const isPdf = url.match(/\.pdf$/i);

    return (
      <div key={index} className="relative group">
        {isImage ? (
          <img
            src={url}
            alt={`Uploaded file ${index + 1}`}
            className="w-40 h-28 object-cover rounded border"
          />
        ) : isVideo ? (
          <div className="w-40 h-28 flex flex-col items-center justify-center border rounded bg-gray-100">
            <span className="text-2xl mb-1">🎥</span>
            <span className="text-xs text-gray-600 truncate px-2">
              {fileName}
            </span>
          </div>
        ) : isPdf ? (
          <div className="w-40 h-28 flex flex-col items-center justify-center border rounded bg-gray-100">
            <span className="text-2xl mb-1">📄</span>
            <span className="text-xs text-gray-600 truncate px-2">
              {fileName}
            </span>
          </div>
        ) : (
          <div className="w-40 h-28 flex flex-col items-center justify-center border rounded bg-gray-100">
            <span className="text-2xl mb-1">📎</span>
            <span className="text-xs text-gray-600 truncate px-2">
              {fileName}
            </span>
          </div>
        )}

        {!disabled && (
          <button
            type="button"
            aria-label="Remove file"
            onClick={() => handleClearFile(index)}
            className="absolute top-1 right-1 bg-black bg-opacity-60 rounded-full p-1 text-white hover:bg-opacity-80 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            ✕
          </button>
        )}
      </div>
    );
  };

  return (
    <div className={`file-upload-component ${className}`}>
      <label className="block mb-1 font-medium">{label}</label>

      {previewUrls.length > 0 ? (
        <div className="mb-4">
          <div className="flex flex-wrap gap-4 mb-2">
            {previewUrls.map((url, index) => renderPreview(url, index))}
          </div>
          {multiple && previewUrls.length > 1 && !disabled && (
            <button
              type="button"
              onClick={handleClearAll}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear All
            </button>
          )}
        </div>
      ) : (
        <div className="w-40 h-28 flex items-center justify-center border rounded bg-gray-100 text-gray-400 mb-4">
          No files
        </div>
      )}

      <div className="flex items-center gap-4">
        <button
          type="button"
          onClick={handleUploadClick}
          disabled={disabled || uploading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 flex items-center gap-2"
        >
          {uploading && (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          )}
          {uploading
            ? 'Uploading...'
            : previewUrls.length > 0
            ? `${multiple ? 'Add More' : 'Change'} Files`
            : 'Upload Files'}
        </button>

        <input
          type="file"
          accept={accept}
          multiple={multiple}
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          disabled={disabled || uploading}
        />

        <div className="text-sm text-gray-500">
          {multiple ? 'Multiple files allowed' : 'Single file only'}
        </div>
      </div>

      <div className="text-xs text-gray-400 mt-1">
        Supports: Images, Videos, PDFs
      </div>
    </div>
  );
};

export default FileUpload;
