import { ICategory } from '@/types/category';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetCategoryBySlug(slug: string) {
  return useQuery<IApiResponse<ICategory>, Error>({
    queryKey: ['category-by-slug', `category-${slug}`],
    queryFn: () =>
      fetch(`/api/category/slug/${slug}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
