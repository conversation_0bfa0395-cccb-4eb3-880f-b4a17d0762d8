'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import FileUpload from '@/components/image/image-upload';
import { UseGetSupport } from '../queries/use-get-home-support';
import { useUpdateSupport } from '../mutations/use-update-home-support';

interface SupportSectionData {
  id: string;
  imageUrl: string | null;
  title: string;
  subtitle: string;
  buttonLabel: string;
  buttonUrl: string;
}

const initialData: SupportSectionData[] = [
  {
    id: '1',
    imageUrl: '/images/image2.webp',
    title: '24/7 Support',
    subtitle: 'We are here whenever you need help.',
    buttonLabel: 'Contact Us',
    buttonUrl: 'https://example.com/contact',
  },
];

const SupportSectionEditPage: React.FC = () => {
  const router = useRouter();
  const { data, isLoading, isError } = UseGetSupport();
  const support = data?.data;

  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [title, setTitle] = useState('');
  const [subTitle, setSubtitle] = useState('');
  const [buttonLabel, setButtonLabel] = useState('');
  const [buttonUrl, setButtonUrl] = useState('');

  useEffect(() => {
    if (support) {
      setImageUrl(support.image ?? null);
      setTitle(support.title);
      setSubtitle(support.subTitle);
      setButtonLabel(support.buttonLabel);
      setButtonUrl(support.buttonUrl);
    }
  }, [support]);

  const { mutate: updateSupport } = useUpdateSupport();


  const handleUpdate = () => {
    if (!support) {
      alert('No support section to update');
      return;
    }
    if (!title.trim()) {
      alert('Title is required');
      return;
    }
    if (!subTitle.trim()) {
      alert('Subtitle is required');
      return;
    }
    if (!buttonLabel.trim()) {
      alert('Button label is required');
      return;
    }
    if (!buttonUrl.trim()) {
      alert('Button URL is required');
      return;
    }
    try {
      new URL(buttonUrl);
    } catch {
      alert('Button URL is invalid');
      return;
    }

    updateSupport(
      {
        ...support,
        image: imageUrl,
        title,
        subTitle,
        buttonLabel,
        buttonUrl,
      },
      {
        onSuccess: () => {
          router.push('/home');
        },
        onError: () => {
          alert('Failed to update support section');
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-md mx-auto">
        <p>Loading support data...</p>
      </div>
    );
  }

  if (isError || !support) {
    return (
      <div className="p-6 max-w-md mx-auto">
        <p>Support section not found or failed to load.</p>
        <Button onClick={() => router.push('/home')}>Back to List</Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-6 max-w-md mx-auto">
        <p>Support item not found.</p>
        <Button onClick={() => router.push('/home')}>Back to List</Button>
      </div>
    );
  }

  return (
    <div className="p-6 container mx-auto bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-6">Edit Support Item</h2>

      <div className="mb-4">
        <FileUpload
          label="Support Image"
          value={imageUrl}
          onChange={(v) =>
            setImageUrl(Array.isArray(v) ? v[0] ?? null : v)
          }
          multiple={false}
          accept="image/*"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Title</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter title"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Subtitle</label>
        <input
          type="text"
          value={subTitle}
          onChange={(e) => setSubtitle(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter subtitle"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Button Label</label>
        <input
          type="text"
          value={buttonLabel}
          onChange={(e) => setButtonLabel(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter button label"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Button URL</label>
        <input
          type="url"
          value={buttonUrl}
          onChange={(e) => setButtonUrl(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="https://example.com"
          disabled={isUpdating}
        />
      </div>

      <div className="flex gap-4">
        <Button
          onClick={handleUpdate}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Update
        </Button>
        <Button
          variant="secondary"
          onClick={() => router.push('/home')}
          className="px-6 py-2"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default SupportSectionEditPage;
