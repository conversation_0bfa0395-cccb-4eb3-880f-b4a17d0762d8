import { IProductTabItem } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateProductTabItem() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IProductTabItem>, Error, IProductTabItem>({
    mutationFn: (data) =>
      fetch(`/api/product-tab-item/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-tab-item'] });
      toast.success('Product-Tab-Item Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Product-Tab-Item');
    },
  });
}
