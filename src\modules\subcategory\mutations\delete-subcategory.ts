import { IApiResponse } from '@/types/response';
import { ISubCategory } from '@/types/sub-category';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteSubCategory() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ISubCategory>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/sub-category/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(async (res) => {
        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.message || 'Failed to delete sub-category');
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sub-category'] });
      toast.success('SUb-Category deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Error deleting sub-category: ${error.message}`);
    },
  });
}
