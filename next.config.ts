import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'api.mustangairworks.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  rewrites: async () => {
    return [
      {
        source: '/api/:path*',
        destination: 'https://api.mustangairworks.com/:path*',
      },
    ];
  },
};

export default nextConfig;
