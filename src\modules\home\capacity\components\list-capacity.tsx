'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Pen, Trash } from 'lucide-react';

interface CapacityItem {
    id: string;
    title: string;
    imageUrl: string;
    subtitle: string;
    link: string;
}

interface OurCapacity {
    mainTitle: string;
    capacities: CapacityItem[];
}

const initialData: OurCapacity = {
    mainTitle: 'Our Capabilities & Services',
    capacities: [
        {
            id: '1',
            title: 'Capacity A',
            imageUrl: '/images/image2.webp',
            subtitle: 'High quality service',
            link: 'https://example.com/capacity-a',
        },
        {
            id: '2',
            title: 'Capacity B',
            imageUrl: '/images/image4.webp',
            subtitle: 'Experienced team',
            link: 'https://example.com/capacity-b',
        },
    ],
};

const OurCapacitySection: React.FC = () => {
    const [data, setData] = useState<OurCapacity>(initialData);
    const router = useRouter();

    const handleDelete = (id: string) => {
        if (window.confirm('Are you sure you want to delete this capacity?')) {
            setData((prev) => ({
                ...prev,
                capacities: prev.capacities.filter((c) => c.id !== id),
            }));
        }
    };

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-3xl font-bold">{data.mainTitle}</h1>
                    <Link href="/home/<USER>/edit">
                        <Button>Edit Capacity</Button>
                    </Link>
                </div>
                {data.capacities.length === 0 ? (
                    <p>No capacities available.</p>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="min-w-full border border-gray-300 rounded">
                            <thead className="bg-gray-100">
                                <tr>
                                    <th className="border px-4 py-2">S.N.</th>
                                    <th className="border px-4 py-2">Title</th>
                                    <th className="border px-4 py-2">Image</th>
                                    <th className="border px-4 py-2">Subtitle</th>
                                    <th className="border px-4 py-2">Link</th>
                                    <th className="border px-4 py-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {data.capacities.map((capacity, idx) => (
                                    <tr
                                        key={capacity.id}
                                        className={idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                                    >
                                        <td className="border px-4 py-2 text-center">{idx + 1}</td>
                                        <td className="border px-4 py-2">{capacity.title}</td>
                                        <td className="border px-4 py-2 text-center">
                                            <Image
                                                src={capacity.imageUrl}
                                                alt={capacity.title}
                                                width={100}
                                                height={60}
                                                className="rounded object-cover inline-block"
                                            />
                                        </td>
                                        <td className="border px-4 py-2">{capacity.subtitle}</td>
                                        <td className="border px-4 py-2">
                                            <a
                                                href={capacity.link}
                                                target="_blank"
                                                rel="noreferrer"
                                                className="text-blue-600 hover:underline"
                                            >
                                                Visit Link
                                            </a>
                                        </td>
                                        <td className="border px-4 py-2 flex gap-2 justify-center">
                                            <Link href={`/home/<USER>/edit`}>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                                >
                                                    <Pen className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleDelete(capacity.id)}
                                                className="inline-flex items-center px-3 py-1 border bg-white border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                            >
                                                <Trash className="w-4 h-4" />
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </section>
    );
};

export default OurCapacitySection;
