import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { toast } from 'sonner';
import { IHero } from '@/types/home';

export function useCreateHero() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHero>, Error, IHero>({
    mutationFn: (data: IHero) =>
      fetch(`/api/home-hero`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['hero'] });
      toast.success('Hero Created Sucessfully');
    },
    onError: () => {
      toast.error('Error Creating Hero');
    },
  });
}
