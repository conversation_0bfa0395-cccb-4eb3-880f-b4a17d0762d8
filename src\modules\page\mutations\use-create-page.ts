import { IPage } from '@/types/page';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface CreatePageData {
  title: string;
  image: string;
  content: string;
  slug: string;
  showOnNav: boolean;
  categoryId?: string;
}

export function useCreatePage() {
  const queryClient = useQueryClient();

  return useMutation<IApiResponse<IPage>, Error, CreatePageData>({
    mutationFn: (data: CreatePageData) =>
      fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        credentials: 'include',
        body: JSON.stringify(data),
      }).then((res) => res.json()),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pages'] });
    },
  });
}
