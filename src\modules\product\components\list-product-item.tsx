"use client";

import { useGetProductTabItemById } from "@/modules/productTabItem/queries/get-product-tab-item-by-id";
import React from "react";
import { IProductTabItem } from "@/types/product";
import { useDeleteProductTabItem } from "@/modules/productTabItem/mutations/delete-product-tab-item";
import { Edit, Trash } from "lucide-react";

type Props = {
  productTabId: string;
  onEdit?: (item: IProductTabItem) => void;
  onDone?: () => void;
};

const ProductTabItemListByTabId: React.FC<Props> = ({ productTabId, onEdit, onDone }) => {
  const { data, isLoading, isError } = useGetProductTabItemById(productTabId);
  console.log("data", data)
  const deleteItem = useDeleteProductTabItem();

  const items: IProductTabItem[] = data?.data ? (Array.isArray(data.data) ? data.data : [data.data]) : [];

  if (!productTabId) return <div>Please provide a valid productTabId.</div>;
  if (isLoading) return <div>Loading items…</div>;
  if (isError) return <div>Error loading items.</div>;
  if (!items) return <div>No items found for this tab.</div>;

  const handleDelete = async (id: string) => {
    try {
      if (!window.confirm("Are you sure you want to delete this item?")) return;
      await deleteItem.mutateAsync(id);
      onDone?.();
    } catch (e) {
      console.error("Failed to delete product-tab item:", e);
    }
  };

  return (
    <div className="overflow-auto max-w-full">
      <table className="w-full border border-gray-300 shadow-sm">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 border-b border-r text-left">Title</th>
            <th className="px-4 py-2 border-b border-r text-left">Description</th>
            <th className="px-4 py-2 border-b text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr key={item.id} className="odd:bg-white even:bg-gray-50">
              <td className="px-4 py-2 border-b border-r">{item.title}</td>
              <td className="px-4 py-2 border-b border-r">
                <div className="w-44 line-clamp-2 break-words" dangerouslySetInnerHTML={{
                  __html: item.description
                }} />
              </td>
              <td className="px-2 py-2 border-b">
                <button
                  onClick={() => onEdit?.(item)}
                  className="text-blue-600 mr-3 inline-flex items-center gap-1 hover:underline"
                  title="Edit"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(item.id)}
                  className="text-red-600 inline-flex items-center gap-1 hover:underline"
                  title="Delete"
                >
                  <Trash className="h-4 w-4" />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProductTabItemListByTabId;
