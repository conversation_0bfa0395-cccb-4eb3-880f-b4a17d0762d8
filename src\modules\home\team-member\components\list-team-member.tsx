'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Pen, Trash } from 'lucide-react';

interface TeamMember {
    id: string;
    imageUrl: string;
    name: string;
    role: string;
}

interface TeamMemberSection {
    mainTitle: string;
    members: TeamMember[];
}

const initialData: TeamMemberSection = {
    mainTitle: 'Meet Our Team',
    members: [
        {
            id: '1',
            imageUrl: '/images/image2.webp',
            name: '<PERSON>',
            role: 'CEO',
        },
        {
            id: '2',
            imageUrl: '/images/image4.webp',
            name: '<PERSON>',
            role: 'CTO',
        },
    ],
};

const TeamMemberSection: React.FC = () => {
    const [data, setData] = useState<TeamMemberSection>(initialData);

    const handleDelete = (id: string) => {
        if (window.confirm('Are you sure you want to delete this team member?')) {
            setData((prev) => ({
                ...prev,
                members: prev.members.filter((m) => m.id !== id),
            }));
        }
    };

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-3xl font-bold">{data.mainTitle}</h1>
                    <Link href="/home/<USER>/edit">
                        <Button>Edit Team Members</Button>
                    </Link>
                </div>

                {data.members.length === 0 ? (
                    <p>No team members available.</p>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="min-w-full text-left border-separate border-spacing-0">
                            <thead className="bg-gray-100">
                                <tr>
                                    <th className="border px-4 py-2">S.N.</th>
                                    <th className="border px-4 py-2">Image</th>
                                    <th className="border px-4 py-2">Name</th>
                                    <th className="border px-4 py-2">Role</th>
                                    <th className="border px-4 py-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {data.members.map((member, idx) => (
                                    <tr
                                        key={member.id}
                                        className={idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                                    >
                                        <td className="border px-4 py-2 text-center">{idx + 1}</td>
                                        <td className="border px-4 py-2 text-center">
                                            <Image
                                                src={member.imageUrl}
                                                alt={member.name}
                                                width={80}
                                                height={80}
                                                className="rounded-full object-cover inline-block"
                                            />
                                        </td>
                                        <td className="border px-4 py-2">{member.name}</td>
                                        <td className="border px-4 py-2">{member.role}</td>
                                        <td className="border px-4 py-2 flex gap-2 justify-center">
                                            <Link href={`/home/<USER>/edit/${member.id}`}>
                                                <Button variant="outline" size="sm"
                                                    className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                                >
                                                    <Pen className="w-4 h-4" /> 
                                                </Button>
                                            </Link>
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleDelete(member.id)}
                                                className="inline-flex items-center px-3 py-1 border bg-white border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                            >
                                                <Trash className="w-4 h-4" /> 
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </section>
    );
};

export default TeamMemberSection;
