'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { IBlog } from '@/types/blog';
import FileUpload from '@/components/image/image-upload';
import dynamic from 'next/dynamic';

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), {
  ssr: false,
});

interface BlogFormProps {
  blog?: IBlog;
  onSubmit: (data: any) => void;
  loading?: boolean;
}

export function BlogForm({ blog, onSubmit, loading }: BlogFormProps) {
  const router = useRouter();

  const [formData, setFormData] = useState({
    title: blog?.title || '',
    image: blog?.image || null,
    content: blog?.content || '',
    slug: blog?.slug || '',
  });

  // Generate slug from title
  useEffect(() => {
    if (!blog && formData.title) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .trim();
      setFormData((prev) => ({ ...prev, slug }));
    }
  }, [formData.title, blog]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>{blog ? 'Edit Blog' : 'Create New Blog'}</CardTitle>
        <CardDescription>
          {blog
            ? 'Update the blog information below.'
            : 'Fill in the details to create a new blog post.'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              placeholder="Enter blog title"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, slug: e.target.value }))
              }
              placeholder="blog-slug"
              required
            />
          </div>

          <FileUpload
            accept="image/*"
            value={formData.image}
            onChange={(imageUrl) =>
              setFormData((prev) => ({
                ...prev,
                image: Array.isArray(imageUrl) ? imageUrl[0] : imageUrl,
              }))
            }
            label="Upload Blog Image"
            disabled={loading}
          />

          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <RichTextEditor
              value={formData.content}
              onChange={(value) =>
                setFormData((prev) => ({ ...prev, content: value }))
              }
            />
          </div>

          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : blog ? 'Update Blog' : 'Create Blog'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
