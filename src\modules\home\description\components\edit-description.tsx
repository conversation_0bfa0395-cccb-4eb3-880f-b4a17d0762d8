'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Textarea } from '@/components/ui/textarea';

const dummyHero = {
  titles: "Description",
};

const EditDescription: React.FC = () => {
  const router = useRouter();

  const [title, setTitle] = useState<string>('');

  useEffect(() => {
    setTitle(dummyHero.titles);
  }, []);

  const handleTitleChange = (value: string) => {
    setTitle(value);
  };

  const handleUpdate = () => {
    if (!title.trim()) {
      alert('Please enter a description');
      return;
    }
    alert('Description section updated (dummy)');
    router.push('/home');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Edit Description Section</h2>

      <div className="mb-6">
        <label className="block font-medium mb-2">Description</label>
        <Textarea
          className="w-full border rounded px-3 py-2"
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          placeholder="Enter description"
          rows={5}
        />
      </div>

      <div className="mt-6 flex space-x-2">
        <button
          onClick={handleUpdate}
          className="px-4 py-2 rounded text-white bg-green-600 hover:bg-green-700"
        >
          Update
        </button>
        <Link href="/home">
          <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default EditDescription;
