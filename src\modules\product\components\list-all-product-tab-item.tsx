"use client";

import React from "react";
import { IProductTabItem } from "@/types/product";
import { useGetProductTabItem } from "@/modules/productTabItem/queries/get-all-product-tabItem";
import { Trash } from "lucide-react";
import { useDeleteProductTabItem } from "@/modules/productTabItem/mutations/delete-product-tab-item";

const ProductTabItemListAll: React.FC = () => {
  const { data, isLoading, isError } = useGetProductTabItem();
  const deleteItem = useDeleteProductTabItem();
  const items: IProductTabItem[] = data?.data ?? [];

  if (isLoading) return <div>Loading items…</div>;
  if (isError) return <div>Error loading items.</div>;
  if (items.length === 0) return <div>No items found.</div>;
  
  const handleDelete = async (id: string) => {
    try {
      if (!window.confirm("Are you sure you want to delete this item?")) return;      
      await deleteItem.mutateAsync(id);
    } catch (e) {
      console.error("Failed to delete product-tab item:", e);
    }
  };

  return (
    <div className="overflow-auto max-w-full">
      <table className="w-full border border-gray-300 shadow-sm">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 border-b border-r text-left">Title</th>
            <th className="px-4 py-2 border-b border-r text-left">Product Tab Name</th>
            <th className="px-4 py-2 border-b border-r text-left">Description</th>
            <th className="px-4 py-2 border-b border-r text-left">Actions</th>
            {/* <th className="px-4 py-2 border-b text-left">Product Tab ID</th> */}
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr key={item.id} className="odd:bg-white even:bg-gray-50">
              <td className="px-4 py-2 border-b border-r">{item.title}</td>
              <td className="px-4 py-2 border-b border-r">{item.productTab?.name}</td>
              <td className="px-4 py-2 border-b border-r">
                <div className="w-44 line-clamp-2 break-words" dangerouslySetInnerHTML={{ 
                  __html: item.description }} />
              </td>
              <td className="px-4 py-2 border-b border-r">
                <button
                  onClick={() => handleDelete(item.id)}
                  className="text-red-600 inline-flex items-center gap-1 hover:underline"
                  title="Delete"
                >
                  <Trash className="h-4 w-4" />
                </button>
              </td>
              {/* <td className="px-4 py-2 border-b">{item.productTabId}</td> */}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProductTabItemListAll;