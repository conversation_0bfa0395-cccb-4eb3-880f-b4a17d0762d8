'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  FolderOpen,
  HomeIcon,
  Info,
  LayersIcon,
  LayoutDashboardIcon,
  Newspaper,
  Package,
  ScrollText,
} from 'lucide-react';

interface MenuItem {
  header?: string;
  title?: string;
  icon?: React.ReactNode;
  to?: string;
  chip?: string;
  BgColor?: string;
  chipBgColor?: string;
  chipColor?: string;
  chipVariant?: string;
  chipIcon?: string;
  children?: MenuItem[];
  disabled?: boolean;
  type?: string;
  subCaption?: string;
}

const sidebarItem: MenuItem[] = [
  { header: 'Home' },
  {
    title: 'Dashboard',
    icon: <LayoutDashboardIcon size={20} className="iconClass" />,
    to: '/',
  },
  { header: 'Home Page' },
  {
    title: 'Page',
    icon: <HomeIcon size={20} className="iconClass" />,
    to: '/home',
  },
  { header: 'Category' },
  {
    title: 'Category',
    icon: <FolderOpen size={20} className="iconClass" />,
    to: '/category',
  },
  {
    title: 'Sub Category',
    icon: <LayersIcon size={20} className="iconClass" />,
    to: '/subcategory',
  },
  {
    title: 'Product',
    icon: <Package size={20} className="iconClass" />,
    to: '/product',
  },
  {
    title: 'Pages',
    icon: <ScrollText size={20} className="iconClass" />,
    to: '/pages',
  },
  {
    title: 'Blogs',
    icon: <Newspaper size={20} className="iconClass" />,
    to: '/blogs',
  },
  {
    title: 'About Us',
    icon: <Info size={20} className="iconClass" />,
    to: '/about-us',
  },

  // {
  //     title: "Button",
  //     icon: <CircleDotIcon size={20} className="iconClass" />,
  //     to: "/ui-components/buttons",
  // },
  // {
  //     title: "Cards",
  //     icon: <CardSimIcon size={20} className="iconClass" />,
  //     to: "/ui-components/cards",
  // },
  // {
  //     title: "Tables",
  //     icon: <TableIcon size={20} className="iconClass" />,
  //     to: "/ui-components/tables",
  // },
  // { header: "Auth" },
  // {
  //     title: "Login",
  //     icon: <Lock size={20} className="iconClass" />,
  //     to: "/auth/login",
  // },
  // {
  //     title: "Register",
  //     icon: <UserPlusIcon size={20} className="iconClass" />,
  //     to: "/auth/register",
  // },
  // { header: "Extra" },
  // {
  //     title: "Icons",
  //     icon: <Sparkle size={20} className="iconClass" />,
  //     to: "/pages/icons",
  // },
  // {
  //     title: "Sample Page",
  //     icon: <ApertureIcon size={20} className="iconClass" />,
  //     to: "/pages/sample-page",
  // },
];

const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const isActive = (to: string) => pathname === to;

  return (
    <nav className="w-64 h-full bg-gray-100 shadow-md py-2">
      <ul>
        {sidebarItem.map((item, idx) =>
          item.header ? (
            <li
              key={idx}
              className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase mt-6"
            >
              {item.header}
            </li>
          ) : (
            <li key={idx}>
              <Link
                href={item.to ?? '#'}
                className={`flex items-center px-4 py-2 text-gray-700 hover:bg-blue-50 rounded-lg transition duration-150
                                ${
                                  isActive(item.to ?? '')
                                    ? 'bg-blue-100 text-blue-600 font-semibold'
                                    : ''
                                }
                                ${
                                  item.disabled
                                    ? 'opacity-50 pointer-events-none'
                                    : ''
                                }`}
              >
                {item.icon}
                <span className="ml-3">{item.title}</span>
              </Link>
            </li>
          )
        )}
      </ul>
    </nav>
  );
};

export default Sidebar;
