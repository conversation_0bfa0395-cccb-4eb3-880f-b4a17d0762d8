import { IHomeSupport } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetSupport() {
  return useQuery<IApiResponse<IHomeSupport>, Error>({
    queryKey: ['support'],
    queryFn: () =>
      fetch(`/api/home-support`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
