'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import FileUpload from '@/components/image/image-upload';

interface TrustedPartnerData {
  id: string;
  description: string;
  images: string[];
}

const dummyData: TrustedPartnerData[] = [
  {
    id: '1',
    description: 'Existing trusted partner description',
    images: [
      '/images/image2.webp',
      '/images/image4.webp',
    ],
  },
];

const TrustedPartnerEditPage: React.FC = () => {
  const router = useRouter();
  const [data, setData] = useState<TrustedPartnerData | null>(null);
  const [showUploader, setShowUploader] = useState(false);

  useEffect(() => {
   
    const found = dummyData[0];
    setData(found);
  }, []);

  const handleDescriptionChange = (value: string) => {
    if (!data) return;
    setData({ ...data, description: value });
  };

  const handleImageAdd = (urls: string | string[] | null) => {
    if (!data) return;
    if (!urls) return;

    let newImages: string[] = [];
    if (Array.isArray(urls)) {
      newImages = urls;
    } else {
      newImages = [urls];
    }

    const combinedImages = [...data.images, ...newImages].slice(0, 3);
    setData({ ...data, images: combinedImages });
    setShowUploader(false);
  };

  const handleRemoveImage = (index: number) => {
    if (!data) return;
    const updatedImages = data.images.filter((_, i) => i !== index);
    setData({ ...data, images: updatedImages });
  };

  const handleUpdate = () => {
    if (!data) return;
    if (!data.description.trim()) {
      alert('Description is required');
      return;
    }
    if (data.images.length === 0) {
      alert('Please upload at least one image');
      return;
    }
    alert('Trusted Partner updated (dummy)');
    router.push('/home/'); 
  };

  if (!data) {
    return (
      <div className="p-6 max-w-md mx-auto">
        <p>Trusted partner not found.</p>
        <Button onClick={() => router.push('/home')}>
          Back to List
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6 container mx-auto">
      <h2 className="text-2xl font-semibold mb-6">Edit Trusted Partner</h2>

      <div className="mb-6">
        <label className="block font-medium mb-2">Description</label>
        <textarea
          className="w-full border rounded p-3 min-h-[120px]"
          value={data.description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          placeholder="Enter description"
        />
      </div>

      <div className="mb-4 flex flex-wrap gap-4">
        {data.images.map((url, idx) => (
          <div key={idx} className="relative w-24 h-24 rounded border overflow-hidden">
            <button
              type="button"
              onClick={() => handleRemoveImage(idx)}
              className="absolute top-1 right-1 z-10 bg-black bg-opacity-60 rounded-full text-white w-6 h-6 flex items-center justify-center hover:bg-opacity-80"
              aria-label={`Remove image ${idx + 1}`}
            >
              &times;
            </button>
            <img
              src={url}
              alt={`Uploaded image ${idx + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>

      <div className="mb-6">
        {!showUploader && data.images.length < 3 && (
          <Button onClick={() => setShowUploader(true)}>Add Image</Button>
        )}

        {showUploader && (
          <FileUpload
            value={null}
            onChange={handleImageAdd}
            multiple={false}
            accept="image/*"
            label="Upload Image"
          />
        )}
      </div>

      <div className="flex gap-4">
        <Button onClick={handleUpdate} className="bg-green-600 hover:bg-green-700 text-white">
          Update
        </Button>
        <Button onClick={() => router.push('/home/<USER>')} variant="secondary">
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TrustedPartnerEditPage;
