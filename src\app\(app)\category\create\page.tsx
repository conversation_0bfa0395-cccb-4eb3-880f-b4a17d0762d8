'use client';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useCreateCategory } from '@/modules/category/mutations/create-category';
import { useRouter } from 'next/navigation';
import React, { useState, FormEvent } from 'react';

const CreateCategoryPage: React.FC = () => {
  const [name, setName] = useState('');
  const [slug, setSlug] = useState('');
  const [showOnNav, setShowOnNav] = useState(false);
  const router = useRouter();

  const createCategoryMutation = useCreateCategory();

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    createCategoryMutation.mutate(
      {
        name,
        slug,
        id: '',
        showOnNav,
      },
      {
        onSuccess: () => {
          router.push('/category');
        },
      }
    );
  };

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen">
      <h1 className="text-2xl font-semibold mb-6 text-">Create Category</h1>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label
            htmlFor="name"
            className="block mb-2 font-medium text-gray-700"
          >
            Name:
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter category name"
          />
        </div>
        <div>
          <label
            htmlFor="slug"
            className="block mb-2 font-medium text-gray-700"
          >
            Slug:
          </label>
          <input
            id="slug"
            type="text"
            value={slug}
            onChange={(e) => setSlug(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter category slug"
          />
        </div>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="showOnNav"
              checked={showOnNav}
              onCheckedChange={(checked) => setShowOnNav(!!checked)}
            />
            <Label
              htmlFor="showOnNav"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Show on Nav
            </Label>
          </div>
        </div>
        <Button
          type="submit"
          className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibold"
        >
          Create
        </Button>
      </form>
    </div>
  );
};

export default CreateCategoryPage;
