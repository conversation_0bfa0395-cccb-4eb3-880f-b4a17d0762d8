import { IProduct } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateProduct() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IProduct>, Error, IProduct>({
    mutationFn: (data) =>
      fetch(`/api/product/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product'] });
      toast.success('Product Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Product');
    },
  });
}
