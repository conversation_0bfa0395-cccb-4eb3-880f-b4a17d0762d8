import { IHomeSupport } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateSupport() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHomeSupport>, Error, IHomeSupport>({
    mutationFn: (data: IHomeSupport) =>
      fetch(`/api/home-support`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Support'] });
      toast.success('Support Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Support');
    },
  });
}
