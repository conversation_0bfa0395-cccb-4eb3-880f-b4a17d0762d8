import { IBlog } from '@/types/blog';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface UpdateBlogData {
  title?: string;
  image?: string;
  content?: string;
  slug?: string;
}

interface UpdateBlogVariables {
  id: string;
  data: UpdateBlogData;
}

export function useUpdateBlog() {
  const queryClient = useQueryClient();

  return useMutation<IApiResponse<IBlog>, Error, UpdateBlogVariables>({
    mutationFn: ({ id, data }: UpdateBlogVariables) =>
      fetch(`/api/blogs/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        credentials: 'include',
        body: JSON.stringify(data),
      }).then((res) => res.json()),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['blog', id] });
    },
  });
}
