import { IBlog } from '@/types/blog';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetBlog(id: string) {
  return useQuery<IApiResponse<IBlog>, Error>({
    queryKey: ['blog', id],
    queryFn: () =>
      fetch(`/api/blogs/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
