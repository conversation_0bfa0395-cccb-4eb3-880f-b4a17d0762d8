import { IProductTab } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreateProductTab() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IProductTab>, Error, IProductTab>({
    mutationFn: (data) =>
      fetch(`/api/product-tab`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-tab'] });
      toast.success('Product Tab added successfully');
    },
    onError: () => {
      toast.error('Error adding Product Tab');
    },
  });
}
