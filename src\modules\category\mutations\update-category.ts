import { ICategory } from '@/types/category';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateCategory() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ICategory>, Error, ICategory>({
    mutationFn: (data) =>
      fetch(`/api/category/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['category'] });
      toast.success('Category Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Category');
    },
  });
}
