'use client';

import React, { useState, useEffect, FormEvent } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import dynamic from 'next/dynamic';
import { useCreateProduct } from '@/modules/product/mutations/create-product';
import { useGetSubCategory } from '@/modules/subcategory/queries/get-all-subcategory';
import { useRouter } from 'next/navigation';
import { useGetCategory } from '@/modules/category/queries/list-category';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import FileUpload from '@/components/image/image-upload';

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), {
  ssr: false,
});

const BasicDetailsForm: React.FC = () => {
  const router = useRouter();
  const createProductMutation = useCreateProduct();
  const {
    data: subCategoriesData,
    isLoading: isSubCategoriesLoading,
    isError: isSubCategoriesError,
  } = useGetSubCategory();

  const subCategories = subCategoriesData?.data ?? [];

  const [name, setName] = useState('');
  const [slug, setSlug] = useState('');
  const [description, setDescription] = useState('');
  const [image, setImage] = useState<string[]>([]);
  const [subCategoryId, setSubCategoryId] = useState<string | null>(null);
  const [showOnNav, setShowOnNav] = useState(false);

  useEffect(() => {
    if (subCategories.length > 0 && !subCategoryId) {
      setSubCategoryId(subCategories[0].id);
    }
  }, [subCategories, subCategoryId]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    if (!subCategoryId) {
      return alert('Please select a sub category');
    }

    createProductMutation.mutate(
      {
        id: '',
        name,
        slug,
        description,
        image,
        subCategoryId,
        showOnNav,
      },
      {
        onSuccess: () => router.push('/product'),
      }
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-5">
      <div>
        <label htmlFor="name" className="block mb-1 font-medium text-gray-700">
          Name:
        </label>
        <Input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
          placeholder="Enter product name"
        />
      </div>

      <div>
        <label htmlFor="slug" className="block mb-1 font-medium text-gray-700">
          Slug:
        </label>
        <Input
          id="slug"
          type="text"
          value={slug}
          onChange={(e) => setSlug(e.target.value)}
          required
          placeholder="Enter product slug"
        />
      </div>

      <div>
        <label
          htmlFor="description"
          className="block mb-1 font-medium text-gray-700"
        >
          Description:
        </label>
        <RichTextEditor value={description} onChange={setDescription} />
      </div>

      <FileUpload
        accept="image/*"
        label="Image Upload"
        multiple
        onChange={(urls) => setImage(Array.isArray(urls) ? urls : [])}
        value={image}
        disabled={createProductMutation.isPending}
      />

      <div>
        <label
          htmlFor="subcategoryId"
          className="block mb-1 font-medium text-gray-700"
        >
          Sub Category:
        </label>
        <Select
          value={subCategoryId || ''}
          onValueChange={setSubCategoryId}
          disabled={isSubCategoriesLoading || isSubCategoriesError}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a subcategory" />
          </SelectTrigger>
          <SelectContent>
            {subCategories.map((subcat) => (
              <SelectItem key={subcat.id} value={subcat.id}>
                {subcat.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="showOnNav"
            checked={showOnNav}
            onCheckedChange={(checked) => setShowOnNav(!!checked)}
          />
          <Label
            htmlFor="showOnNav"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Show on Nav
          </Label>
        </div>
      </div>

      <Button
        type="submit"
        disabled={createProductMutation.isPending}
        className="bg-green-600 text-white py-2 px-6 rounded hover:bg-green-700 transition font-semibold"
      >
        Create
      </Button>
    </form>
  );
};

export default BasicDetailsForm;
