import { IProductTab } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteProductTab() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IProductTab>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/product-tab/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-tab'] });
      toast.success('Product-Tab Deleted Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Product-Tab');
    },
  });
}
