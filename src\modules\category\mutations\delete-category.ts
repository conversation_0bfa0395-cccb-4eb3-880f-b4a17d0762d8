import { ICategory } from '@/types/category';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteCategory() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ICategory>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/category/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(async (res) => {
        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.message || 'Failed to delete category');
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['category'] });
      toast.success('Category deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Error deleting category: ${error.message}`);
    },
  });
}
