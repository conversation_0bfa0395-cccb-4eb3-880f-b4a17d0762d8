"use client";

import React, { useEffect, useState, FormEvent } from "react";
import dynamic from "next/dynamic";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select, SelectTrigger, SelectValue, SelectContent, SelectItem,
} from "@/components/ui/select";

import { useCreateProductTabItem } from "@/modules/productTabItem/mutations/create-product-tab-item";
import { useGetProductTab } from "@/modules/productTab/queries/get-all-product-tab";
import { IProductTabItem } from "@/types/product";
import { useUpdateProductTabItem } from "@/modules/productTabItem/mutations/update-product-tab-item";

const RichTextEditor = dynamic(() => import("@/utils/ck-editor"), { ssr: false });

type Props = {
  productTabId?: string;
  productIdFilter?: string;
  initialItem?: IProductTabItem;
  onSuccess?: () => void;
  onClose?: () => void;
};

const ProductTabItemForm: React.FC<Props> = ({ productTabId, productIdFilter, initialItem, onSuccess, onClose }) => {
  const createItem = useCreateProductTabItem();
  const updateItem = useUpdateProductTabItem();

  const { data: tabsResp, isLoading: tabsLoading, isError: tabsError } = useGetProductTab();
  const allTabs = tabsResp?.data ?? [];
  const tabs = productIdFilter
    ? allTabs.filter((t) => t.productId === productIdFilter)
    : allTabs;

  const isEditing = !!initialItem;

  const [selectedTabId, setSelectedTabId] = useState<string>(productTabId ?? "");
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");

  useEffect(() => {
    setSelectedTabId(initialItem?.productTabId ?? productTabId ?? "");
    setTitle(initialItem?.title ?? "");
    setDescription(initialItem?.description ?? "");
  }, [initialItem, productTabId]);

  useEffect(() => {
    if (productTabId) {
      setSelectedTabId(productTabId);
      return;
    }
    if (!selectedTabId && tabs.length > 0) {
      setSelectedTabId(tabs[0].id);
    }
  }, [productTabId, tabs, selectedTabId]);

  const submit = (e: FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;
    if (!selectedTabId) return;

    if (isEditing) {
      updateItem.mutate(
        {
          id: initialItem!.id,
          title,
          description,
          productTabId: selectedTabId,
        },
        {
          onSuccess: () => {
            onSuccess?.();
          },
        }
      );
    } else {
      createItem.mutate(
        { id: "", title, description, productTabId: selectedTabId },
        {
          onSuccess: () => {
            setTitle("");
            setDescription("");
            onSuccess?.();
          },
        }
      );
    }
  };

  const locked = Boolean(productTabId);

  return (
    <form onSubmit={submit} className="space-y-4 max-w-xl">
      {!locked && (
        <div>
          <label className="block mb-1 font-medium text-gray-700">Product Tab</label>
          {tabsLoading ? (
            <p className="text-sm text-muted-foreground">Loading tabs…</p>
          ) : tabsError ? (
            <p className="text-sm text-red-600">Failed to load tabs.</p>
          ) : tabs.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No tabs found{productIdFilter ? " for this product" : ""}.
            </p>
          ) : (
            <Select value={selectedTabId} onValueChange={setSelectedTabId}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a product tab" />
              </SelectTrigger>
              <SelectContent>
                {tabs.map((t) => (
                  <SelectItem key={t.id} value={t.id}>
                    {t.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      )}

      {locked && (
        <>
          <input type="hidden" value={selectedTabId} readOnly />
          <p className="text-sm text-muted-foreground">
            Posting to tab: <span className="font-medium">{selectedTabId}</span>
          </p>
        </>
      )}

      <div>
        <label className="block mb-1 font-medium text-gray-700">Title</label>
        <Input
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter item title"
          required
        />
      </div>

      <div>
        <label className="block mb-1 font-medium text-gray-700">Description</label>
        <RichTextEditor value={description} onChange={setDescription} />
      </div>

      <div className="flex items-center gap-3">
        <Button
          type="submit"
          disabled={
            createItem.isPending ||
            updateItem.isPending ||
            (!locked && (!selectedTabId || tabs.length === 0))
          }
          className="bg-blue-600 text-white font-semibold"
        >
          {isEditing ? (updateItem.isPending ? "Updating…" : "Update Item") : (createItem.isPending ? "Creating…" : "Create Item")}
        </Button>

        {isEditing && (
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
};

export default ProductTabItemForm;
