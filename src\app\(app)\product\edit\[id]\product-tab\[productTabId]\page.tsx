'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import ProductTabItemForm from '@/modules/product/components/add-product-tab-item';
import ProductTabItemListByTabId from '@/modules/product/components/list-product-item';
import type { IProductTabItem } from '@/types/product';

export default function ProductTabItemsPage() {
  const { id, productTabId } = useParams<{ id: string; productTabId: string }>();

  const [editingItem, setEditingItem] = useState<IProductTabItem | null>(null);

  return (
    <div className="p-6 container mx-auto min-h-screen">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Product Tab Items</h1>
          <p className="text-sm text-muted-foreground">
            Tab ID: <span className="font-mono">{productTabId}</span>
          </p>
        </div>
        <Link href={`/product/edit/${id}`} className="text-blue-600 hover:underline">
          ← Back to Product
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-medium mb-4">Items in this Tab</h2>
            <ProductTabItemListByTabId
              productTabId={productTabId}
              onEdit={(item) => setEditingItem(item)}
              onDone={() => setEditingItem(null)}
            />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-medium mb-1">
              {editingItem ? 'Edit Item' : 'Create New Item'}
            </h2>
            <p className="text-sm text-muted-foreground mb-4">
              {editingItem ? 'Update the fields and save.' : 'Add a new item to this tab.'}
            </p>

            <ProductTabItemForm
              key={editingItem?.id ?? 'new'}          
              productTabId={productTabId}             
              initialItem={editingItem ?? undefined}  
              onSuccess={() => setEditingItem(null)} 
              onClose={() => setEditingItem(null)}   
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
