import { IBlog } from '@/types/blog';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface CreateBlogData {
  title: string;
  image: string;
  content: string;
  slug: string;
}

export function useCreateBlog() {
  const queryClient = useQueryClient();

  return useMutation<IApiResponse<IBlog>, Error, CreateBlogData>({
    mutationFn: (data: CreateBlogData) =>
      fetch('/api/blogs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        credentials: 'include',
        body: JSON.stringify(data),
      }).then((res) => res.json()),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
    },
  });
}
