import { IProductTabItem } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteProductTabItem() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IProductTabItem>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/product-tab-item/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-tab-item'] });
      toast.success('Product-Tab-Item Deleted Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Product-Tab-Item');
    },
  });
}
