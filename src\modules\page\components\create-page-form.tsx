'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useGetCategory } from '@/modules/category/queries/list-category'; // Assuming you have this
import { IPage } from '@/types/page';
import FileUpload from '@/components/image/image-upload';
import dynamic from 'next/dynamic';

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), {
  ssr: false,
});

interface PageFormProps {
  page?: IPage;
  onSubmit: (data: any) => void;
  loading?: boolean;
}

export function PageForm({ page, onSubmit, loading }: PageFormProps) {
  const router = useRouter();

  const { data: categoriesResponse } = useGetCategory();
  const categories = categoriesResponse?.data || [];

  const [formData, setFormData] = useState({
    title: page?.title || '',
    image: page?.image || '',
    content: page?.content || '',
    slug: page?.slug || '',
    showOnNav: page?.showOnNav || false,
    categoryId: page?.categoryId || '',
  });

  // Generate slug from title
  useEffect(() => {
    if (!page && formData.title) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .trim();
      setFormData((prev) => ({ ...prev, slug }));
    }
  }, [formData.title, page]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>{page ? 'Edit Page' : 'Create New Page'}</CardTitle>
        <CardDescription>
          {page
            ? 'Update the page information below.'
            : 'Fill in the details to create a new page.'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              placeholder="Enter page title"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, slug: e.target.value }))
              }
              placeholder="page-slug"
              required
            />
          </div>

          <FileUpload
            accept="image/*"
            label="Upload Page Image"
            multiple={false}
            value={formData.image}
            onChange={(imageUrl) =>
              setFormData((prev) => ({
                ...prev,
                image: (Array.isArray(imageUrl) ? imageUrl[0] : imageUrl) || '',
              }))
            }
          />

          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <RichTextEditor
              value={formData.content}
              onChange={(content) =>
                setFormData((prev) => ({ ...prev, content }))
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.categoryId || ''}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, categoryId: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="showOnNav"
              checked={formData.showOnNav}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, showOnNav: checked }))
              }
            />
            <Label htmlFor="showOnNav">Show in navigation</Label>
          </div>

          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : page ? 'Update Page' : 'Create Page'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
