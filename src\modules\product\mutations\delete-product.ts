import { IProduct } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteProduct() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IProduct>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/product/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product'] });
      toast.success('Product Deleted Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Product');
    },
  });
}
