'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Pen, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';

const dummyHero = {
  title: 'Welcome to Our Site',
};

const DescriptionSection: React.FC = () => {
  const [hero, setHero] = useState<{ title: string } | null>(dummyHero);
  const router = useRouter();

  const handleDelete = () => {
    if (!hero) return;
    const confirmed = window.confirm('Are you sure you want to delete the hero?');
    if (confirmed) {
      setHero(null);
      alert('Hero deleted successfully');
    }
  };

  if (!hero) {
    return (
      <div className="p-6">
        <p className="mb-6">No description section found.</p>
        <button
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
          onClick={() => setHero(dummyHero)}
        >
          Create Description Section
        </button>
      </div>
    );
  }

  return (
    <section className="p-6">
      <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Description Section</h1>
          <Button onClick={() => router.push('/home/<USER>/edit')}>Edit</Button>
        </div>
        <div className="bg-white rounded-lg shadow">
          <table className="min-w-full text-left border-separate border-spacing-0">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-4 py-2">SN</th>
                <th className="border py-3 px-4">Description</th>
                <th className="border py-3 px-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-t last:border-b hover:bg-gray-100 transition">
                <td className="border py-2 px-4">1</td>
                <td className="border py-2 px-4">{hero.title}</td>
                <td className="py-2 px-4 flex gap-2">
                  <Link
                    href="/home/<USER>/edit"
                    className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                  >
                    <Pen className="w-4 h-4" />
                  </Link>
                  <button
                    onClick={handleDelete}
                    className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                    type="button"
                  >
                    <Trash className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default DescriptionSection;
