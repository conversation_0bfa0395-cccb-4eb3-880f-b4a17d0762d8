import { IProduct } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetProductById(id: string) {
  return useQuery<IApiResponse<IProduct>, Error>({
    queryKey: ['product', id],
    queryFn: () =>
      fetch(`/api/product/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id, 
  });
}
