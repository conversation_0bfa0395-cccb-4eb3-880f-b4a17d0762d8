'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Pen, Trash } from 'lucide-react';

interface TrustedPartnerData {
    id: string;
    description: string;
    images: (string | null)[];
}

const dummyList: TrustedPartnerData[] = [
    {
        id: '1',
        description: 'Existing trusted partner description',
        images: [
            '/images/image2.webp',
            '/images/image4.webp',
        ],
    },
];

const TrustedPartnerListPage: React.FC = () => {
    const [list, setList] = useState<TrustedPartnerData[]>(dummyList);

    const handleDelete = (id: string) => {
        const confirmed = window.confirm('Are you sure you want to delete this trusted partner?');
        if (confirmed) {
            setList(list.filter((item) => item.id !== id));
            alert('Trusted Partner deleted (dummy)');
        }
    };

    if (list.length === 0) {
        return (
            <div className="p-6">
                <p className="mb-6">No trusted partners found.</p>
                <Link href="/home/<USER>/create">
                    <Button>Create Trusted Partner</Button>
                </Link>
            </div>
        );
    }

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-3xl font-bold">Trusted Partners</h1>
                    <Link href="/home/<USER>/edit">
                        <Button>Edit</Button>
                    </Link>
                </div>

                <div className="overflow-x-auto bg-white rounded shadow">
                    <table className="min-w-full table-auto text-left">
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="border px-4 py-2">S.N.</th>
                                <th className="border px-4 py-2">Description</th>
                                <th className="border px-4 py-2 w-48">Images</th>
                                <th className="border px-4 py-2 w-40">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {list.map(({ id, description, images }) => (
                                <tr key={id} className="border-t last:border-b hover:bg-gray-100 transition">
                                    <td className="border px-4 py-2 align-top">{id}</td>

                                    <td className="border px-4 py-2 align-top max-w-xs truncate" title={description}>
                                        {description}
                                    </td>

                                    <td className="border px-4 py-2 align-top">
                                        <div className="flex gap-2 flex-wrap max-w-xs">
                                            {images.map(
                                                (url, idx) =>
                                                    url && (
                                                        <img
                                                            key={idx}
                                                            src={url}
                                                            alt={`Trusted partner ${id} image ${idx + 1}`}
                                                            className="w-12 h-12 object-cover rounded border"
                                                        />
                                                    ),
                                            )}
                                        </div>
                                    </td>

                                    <td className="border px-4 py-2 align-top">
                                        <div className="flex gap-2">
                                            <Link href={`/home/<USER>/edit`}>
                                                <button className="px-3 py-1 border border-green-600 text-green-600 rounded hover:bg-green-50 transition-colors">
                                                    <Pen className="h-4 w-4" />
                                                </button>
                                            </Link>
                                            <button
                                                onClick={() => handleDelete(id)}
                                                className="px-3 py-1 border border-red-600 text-red-600 rounded hover:bg-red-50 transition-colors"
                                                type="button"
                                            >
                                                <Trash className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    );
};

export default TrustedPartnerListPage;
