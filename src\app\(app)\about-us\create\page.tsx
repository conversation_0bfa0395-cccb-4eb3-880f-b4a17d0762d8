'use client';

import { useRouter } from 'next/navigation';
import { PageForm } from '@/modules/page/components/create-page-form';
import { useCreatePage } from '@/modules/page/mutations/use-create-page';
import { AboutUsForm } from '@/modules/page/components/about-us-form';

export default function CreatePagePage() {
  const router = useRouter();
  const createPage = useCreatePage();

  const handleSubmit = async (data: any) => {
    try {
      await createPage.mutateAsync(data);
      router.push('/about-us');
    } catch (error) {
      console.error('Failed to create page:', error);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <AboutUsForm onSubmit={handleSubmit} loading={createPage.isPending} />
    </div>
  );
}
