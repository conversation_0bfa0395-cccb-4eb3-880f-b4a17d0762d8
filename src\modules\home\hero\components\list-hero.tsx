"use client"

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Pen, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UseGetHero } from '../queries/use-get-home-hero';
import { UseDeleteHero } from '../mutations/use-delete-home-hero';

const HeroSection: React.FC = () => {
    const router = useRouter();
    const { data } = UseGetHero();
    const { mutate: deleteHero } = UseDeleteHero();

    const hero = data?.data || null;

    const handleDelete = () => {
        if (!hero) return;
        const confirmed = window.confirm('Are you sure you want to delete the hero?');
        if (!confirmed) return;

        deleteHero(undefined, {
            onSuccess: () => {
                alert('Hero deleted successfully');
            },
            onError: () => {
                alert('Failed to delete hero');
            },
        });
    };

    if (!hero) {
        return (
            <div className="p-6">
                <p className="mb-6">No hero section found.</p>
                <Link href="/home/<USER>/create">
                    <button className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition">
                        Create Hero Section
                    </button>
                </Link>
            </div>
        );
    }

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-3xl font-bold">Hero Section</h1>
                    <Button onClick={() => router.push('/home/<USER>/edit')}>Edit</Button>
                </div>
                <div className="bg-white rounded-lg shadow">
                    <table className="min-w-full text-left border-separate border-spacing-0">
                        <thead>
                            <tr className="bg-gray-100">
                                <th className="border px-4 py-2">SN</th>
                                <th className="border py-3 px-4">Titles</th>
                                <th className="border py-3 px-4">Images</th>
                                <th className="border py-3 px-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className="border-t last:border-b hover:bg-gray-100 transition">
                                <td className="border py-2 px-4">1</td>
                                <td className="border py-2 px-4">
                                    {hero.titles.map((title: string, i: number) => (
                                        <div key={i}>{title}</div>
                                    ))}
                                </td>
                                <td className="border py-2 px-4">
                                    <div className="flex gap-2">
                                        {hero.images.map((img: string, i: number) => (
                                            <Image
                                                key={i}
                                                src={img}
                                                alt={`Hero image ${i}`}
                                                width={60}
                                                height={40}
                                                className="rounded object-cover"
                                                style={{ width: '60px', height: '40px' }}
                                            />
                                        ))}
                                    </div>
                                </td>
                                <td className="py-2 px-4 flex gap-2">
                                    <Link
                                        href="/home/<USER>/edit"
                                        className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                    >
                                        <Pen className="w-4 h-4" />
                                    </Link>
                                    <button
                                        onClick={handleDelete}
                                        className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                        type="button"
                                    >
                                        <Trash className="w-4 h-4" />
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    );
};

export default HeroSection;
