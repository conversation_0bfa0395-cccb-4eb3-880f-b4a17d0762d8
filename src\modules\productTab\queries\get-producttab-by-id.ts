import { IProductTab } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetProductTabById(id: string) {
  return useQuery<IApiResponse<IProductTab>, Error>({
    queryKey: ['product-tab', id],
    queryFn: () =>
      fetch(`/api/product-tab/product/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
