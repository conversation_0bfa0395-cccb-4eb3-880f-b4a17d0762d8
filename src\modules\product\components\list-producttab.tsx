'use client';

import React, { useState } from 'react';
import { IProductTab } from '@/types/product';
import { useGetProductTabById } from '@/modules/productTab/queries/get-producttab-by-id';
import { Edit, List, Trash } from 'lucide-react';
import { useDeleteProductTab } from '@/modules/productTab/mutations/delete-product-tab';
import Link from 'next/link';

interface ProductTabListByProductIdProps {
  productId: string;
  onEdit?: (tab: IProductTab) => void;
  onDone?: () => void;
}

const ProductTabListByProductId: React.FC<ProductTabListByProductIdProps> = ({
  productId,
  onEdit,
  onDone,
}) => {
  const { data, isLoading, isError } = useGetProductTabById(productId);
  const deleteTabMutation = useDeleteProductTab();

  const productTabs: IProductTab[] = data?.data
    ? Array.isArray(data.data)
      ? data.data
      : [data.data]
    : [];


  const handleDelete = async (id: string) => {
    try {
      if (!window.confirm("Are you sure you want to delete this tab?")) return;
      await deleteTabMutation.mutateAsync(id);
      onDone?.();
    } catch (err) {
      console.error('Failed to delete product-tab:', err);
    }
  };

  if (!productId) {
    return <div>Please provide a valid product ID.</div>;
  }

  if (isLoading) {
    return <div>Loading product tabs...</div>;
  }

  if (isError) {
    return <div>Error loading product tabs.</div>;
  }

  if (productTabs.length === 0) {
    return <div>No product tabs found for this product.</div>;
  }

  return (
    <div className="overflow-auto max-w-full">
      <table className="w-full border border-gray-300 shadow-sm">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 border-b border-r border-gray-300 text-left">
              Tab Name
            </th>
            <th className="px-4 py-2 border-b border-r border-gray-300 text-left">
              Product Name
            </th>
            <th className="px-4 py-2 border-b border-r border-gray-300 text-left">
              Manage Items
            </th>
            {/* 
              <th className="px-4 py-2 border-b border-gray-300 text-left">
                Product ID
              </th> */}
            <th className="px-4 py-2 border-b border-gray-300 text-left">
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          {productTabs.map((tab) => (
            <tr
              key={tab.name + tab.productId}
              className="odd:bg-white even:bg-gray-50"
            >
              <td className="px-4 py-2 border-b border-r border-gray-300">
                {tab.name}
              </td>
              <td className="px-4 py-2 border-b border-r border-gray-300">
                {tab.product?.name}
              </td>
              {/* <td className="px-4 py-2 border-b border-gray-300">
                {tab.productId}
              </td> */}
              <td className="px-4 py-2 border-b border-r border-gray-300">
                <Link
                  href={`/product/edit/${productId}/product-tab/${tab.id}`}
                  className="text-emerald-700 mr-3 inline-flex items-center gap-1 hover:underline"
                  title="Manage items in this tab"
                >
                  <List className="h-4 w-4" />
                  <span className="sr-only">Manage Items</span>
                </Link>
              </td>
              <td>
                <button
                  onClick={() => onEdit?.(tab)}
                  className="text-blue-600 mr-3 inline-flex items-center gap-1 hover:underline"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button onClick={() => handleDelete(tab.id)} className="text-red-600">
                  <Trash className="h-4 w-4" />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProductTabListByProductId;
