"use client";

import React, { useState, useEffect, FormEvent } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select, SelectTrigger, SelectValue, SelectContent, SelectItem,
} from "@/components/ui/select";
import { useCreateProductTab } from "@/modules/productTab/mutations/create-product-tab";
import { useGetProduct } from "@/modules/product/queries/get-all-product";
import { IProductTab } from "@/types/product";
import { useUpdateProductTab } from "@/modules/productTab/mutations/update-product-tab";

type Props = {
  onSuccessRedirect?: string;
  fixedProductId?: string; // NEW
  initialTab?: IProductTab;
  onClose?: () => void;
};

const ProductTabForm: React.FC<Props> = ({ onSuccessRedirect, fixedProductId, initialTab, onClose }) => {
  const createProductTabMutation = useCreateProductTab();
  const updateProductTabMutation = useUpdateProductTab();
  const { data, isLoading, isError } = useGetProduct();
  const products = data?.data ?? [];

  const [name, setName] = useState("");
  const [productId, setProductId] = useState(initialTab?.productId ?? fixedProductId ?? "");

  const isEditing = !!initialTab; // NEW


  useEffect(() => {
    setName(initialTab?.name ?? "");
    setProductId(initialTab?.productId ?? fixedProductId ?? "");
  }, [initialTab, fixedProductId]);

  useEffect(() => {
    if (fixedProductId) {
      setProductId(fixedProductId);
    } else if (products.length > 0 && !productId) {
      setProductId(products[0].id);
    }
  }, [fixedProductId, products, productId]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return alert("Please enter a tab name.");
    if (!productId) return alert("No product selected.");

    if (initialTab) {
      updateProductTabMutation.mutate(
        { ...initialTab, name, productId },
        {
          onSuccess: () => {
            if (onSuccessRedirect) window.location.href = onSuccessRedirect;
            setName("");
            if (onClose) onClose();
          },
        }
      );
      if (onClose) onClose();
    } else {
      createProductTabMutation.mutate(
        { id: '', name, productId },
        {
          onSuccess: () => {
            if (onSuccessRedirect) window.location.href = onSuccessRedirect;
            setName("");
            if (onClose) onClose();
          },
        }
      );
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-lg">
      <div>
        <label htmlFor="name" className="block mb-1 font-medium text-gray-700">
          Product Tab Name
        </label>
        <Input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter tab name"
          required
        />
      </div>

      {/* If fixedProductId is provided, hide the selector and pass a hidden input */}
      {fixedProductId ? (
        <>
          <input type="hidden" value={productId} readOnly />
          <p className="text-sm text-muted-foreground">
            For product: <span className="font-medium">{fixedProductId}</span>
          </p>
        </>
      ) : (
        <div>
          <label className="block mb-1 font-medium text-gray-700">Select Product</label>
          <Select value={productId} onValueChange={setProductId} disabled={isLoading || isError}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={isLoading ? 'Loading products...' : 'Select a product'} />
            </SelectTrigger>
            <SelectContent>
              {products.map((p) => (
                <SelectItem key={p.id} value={p.id}>{p.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="flex items-center gap-3">
        <Button type="submit" className="bg-blue-600 text-white py-2 px-6 rounded hover:bg-blue-700 transition font-semibold">
          {isEditing ? 'Update' : 'Create'}
        </Button>
        {isEditing && (
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
        )}
      </div>
    </form>

  );
};

export default ProductTabForm;
