import { IApiResponse } from '@/types/response';
import { ISubCategory } from '@/types/sub-category';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreateSubCategory() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ISubCategory>, Error, ISubCategory>({
    mutationFn: (data) =>
      fetch(`/api/sub-category`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sub-category'] });
      toast.success('Subcategory added successfully');
    },
    onError: () => {
      toast.error('Error adding subcategory');
    },
  });
}
