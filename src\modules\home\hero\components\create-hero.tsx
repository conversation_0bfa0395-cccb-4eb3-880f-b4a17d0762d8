'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useCreateHero } from '../mutations/use-create-home-hero';
import FileUpload from '@/components/image/image-upload';

const CreateHeroPage: React.FC = () => {
    const router = useRouter();
    const [titles, setTitles] = useState<string[]>(['']);
    const [images, setImages] = useState<string[]>([]);

    const { mutate } = useCreateHero();


    const handleTitleChange = (index: number, value: string) => {
        const newTitles = [...titles];
        newTitles[index] = value;
        setTitles(newTitles);
    };

    const handleAddTitle = () => {
        setTitles([...titles, '']);
    };

    const handleRemoveTitle = (index: number) => {
        if (titles.length > 1) {
            setTitles(titles.filter((_, i) => i !== index));
        }
    };

    const handleCreate = () => {
        const validTitles = titles.filter((t) => t.trim());
        if (validTitles.length === 0) {
            alert('Please enter at least one title');
            return;
        }
        if (images.length === 0) {
            alert('Please add at least one image');
            return;
        }
        mutate(
            { id: '', titles: validTitles, images },
            {
                onSuccess: () => {
                    setTitles(['']);
                    setImages([]);
                    router.push('/home');
                }
            }
        );
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            <h2 className="text-2xl font-semibold mb-4">Add New Hero</h2>

            <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                    <label className="block font-medium">Titles *</label>
                    <Button
                        onClick={handleAddTitle}
                        className="px-3 py-1 text-sm bg-blue-600  rounded hover:bg-blue-700"
                    >
                        Add Title
                    </Button>
                </div>
                {titles.map((title, index) => (
                    <div key={index} className="flex items-center gap-2 mb-2">
                        <input
                            type="text"
                            className="flex-1 border rounded px-3 py-2"
                            value={title}
                            onChange={(e) => handleTitleChange(index, e.target.value)}
                            placeholder={`Enter title ${index + 1}`}
                        />
                        {titles.length > 1 && (
                            <button
                                type="button"
                                onClick={() => handleRemoveTitle(index)}
                                className="px-2 py-2 text-red-600 hover:bg-red-50 rounded"
                            >
                                ✕
                            </button>
                        )}
                    </div>
                ))}
            </div>

            <div className="mb-6">
                <FileUpload
                    value={images}
                    onChange={(urls) => setImages(urls as string[])}
                    multiple={true}
                    accept="image/*"
                    label="Images"
                    className="mb-2"
                />
            </div>


            <div className="mt-6 flex space-x-2">
                <button
                    onClick={handleCreate}
                    className="px-4 py-2 rounded text-white bg-green-600 hover:bg-green-700"
                >
                    Create
                </button>
                <Link href="/home">
                    <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
                        Cancel
                    </button>
                </Link>
            </div>
        </div>
    );
};

export default CreateHeroPage;
