import { IPage } from '@/types/page';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPage(id: string) {
  return useQuery<IApiResponse<IPage>, Error>({
    queryKey: ['page', id],
    queryFn: () =>
      fetch(`/api/pages/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
