import { IPage } from '@/types/page';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPages() {
  return useQuery<IApiResponse<IPage[]>, Error>({
    queryKey: ['pages'],
    queryFn: () =>
      fetch(`/api/pages`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}

export function useGetPagesByCategory(category: string) {
  return useQuery<IApiResponse<IPage[]>, Error>({
    queryKey: ['pages-by-category', `page-${category}`, 'pages'],
    queryFn: () =>
      fetch(`/api/pages/category/slug/${category}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
