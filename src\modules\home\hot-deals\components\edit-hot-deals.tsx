'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface HotDeal {
  id: string;
  name: string;
  price: string;
}

const initialDeals: HotDeal[] = [
  { id: '1', name: 'LH MAIN WINDSHIELD', price: '$2,450' },
  { id: '2', name: 'TOP ASSEMBLY', price: '$1,850' },
  { id: '3', name: 'ENGINE', price: '$10,000' },
];

const EditAllHotDealsPage: React.FC = () => {
  const [deals, setDeals] = useState<HotDeal[]>(initialDeals);

  const handleChange = (id: string, field: 'name' | 'price', value: string) => {
    setDeals((prev) =>
      prev.map((deal) =>
        deal.id === id ? { ...deal, [field]: value } : deal
      )
    );
  };

  const handleAddDeal = () => {
    const newDeal: HotDeal = {
      id: (deals.length + 1 + Math.random()).toString(),
      name: '',
      price: '',
    };
    setDeals((prev) => [...prev, newDeal]);
  };

  const handleRemoveDeal = (id: string) => {
    if (deals.length === 1) return; 
    setDeals((prev) => prev.filter((deal) => deal.id !== id));
  };

  const handleSaveAll = () => {
    const allValid = deals.every(
      (deal) => deal.name.trim() !== '' && deal.price.trim() !== ''
    );
    if (!allValid) {
      alert('Please fill in all name and price fields.');
      return;
    }
    alert('All hot deals saved');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen container mx-auto">
      <h2 className="text-2xl font-semibold mb-6">Edit All Hot Deals</h2>

      {deals.map((deal, index) => (
        <div key={deal.id} className="mb-4 flex gap-2 items-center">
          <input
            type="text"
            placeholder="Name"
            value={deal.name}
            onChange={(e) => handleChange(deal.id, 'name', e.target.value)}
            className="flex-1 border rounded px-3 py-2"
          />
          <input
            type="text"
            placeholder="Price"
            value={deal.price}
            onChange={(e) => handleChange(deal.id, 'price', e.target.value)}
            className="w-32 border rounded px-3 py-2"
          />
          {deals.length > 1 && (
            <button
              type="button"
              onClick={() => handleRemoveDeal(deal.id)}
              className="px-2 py-2 text-red-600 hover:bg-red-50 rounded"
              aria-label={`Remove deal ${index + 1}`}
            >
              ✕
            </button>
          )}
        </div>
      ))}

      <div className="flex items-center gap-4 mb-6">
        <Button onClick={handleAddDeal} className="px-4 py-2">
          Add Deal
        </Button>
      </div>

      <div className="flex gap-4">
        <Button
          onClick={handleSaveAll}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Save All
        </Button>
        <Link href="/home">
          <button className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default EditAllHotDealsPage;
