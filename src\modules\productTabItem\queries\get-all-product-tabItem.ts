import { IProductTabItem } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetProductTabItem() {
  return useQuery<IApiResponse<IProductTabItem[]>, Error>({
    queryKey: ['product-tab-item'],
    queryFn: () =>
      fetch(`/api/product-tab-item`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
