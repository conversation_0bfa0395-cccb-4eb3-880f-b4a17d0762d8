'use client';

import { useRouter } from 'next/navigation';
import { BlogForm } from '@/modules/blog/components/blog-form';
import { useGetBlog } from '@/modules/blog/queries/use-get-blog';
import { useUpdateBlog } from '@/modules/blog/mutations/use-update-blog';

export default function EditBlogPage({ id }: { id: string }) {
  const router = useRouter();
  const { data: blogResponse, isLoading } = useGetBlog(id);
  const updateBlog = useUpdateBlog();

  const blog = blogResponse?.data;

  const handleSubmit = async (data: any) => {
    try {
      await updateBlog.mutateAsync({ id: id, data });
      router.push('/blogs');
    } catch (error) {
      console.error('Failed to update blog:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading blog...</div>
        </div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">Blog not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <BlogForm
        blog={blog}
        onSubmit={handleSubmit}
        loading={updateBlog.isPending}
      />
    </div>
  );
}
