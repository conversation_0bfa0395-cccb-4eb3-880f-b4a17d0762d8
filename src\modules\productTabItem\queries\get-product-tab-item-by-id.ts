import { IProductTabItem } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetProductTabItemById(id: string) {
  return useQuery<IApiResponse<IProductTabItem>, Error>({
    queryKey: ['product-tab-item', id],
    queryFn: () =>
      fetch(`/api/product-tab-item/product-tab/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id, 
  });
}
