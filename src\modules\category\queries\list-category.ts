import { ICategory } from '@/types/category';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetCategory() {
  return useQuery<IApiResponse<ICategory[]>, Error>({
    queryKey: ['category'],
    queryFn: () =>
      fetch(`/api/category`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
