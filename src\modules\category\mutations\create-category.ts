import { ICategory } from '@/types/category';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreateCategory() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ICategory>, Error, ICategory>({
    mutationFn: (data) =>
      fetch(`/api/category`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['category'] });
      toast.success('Category added successfully');
    },
    onError: () => {
      toast.error('Error adding category');
    },
  });
}
