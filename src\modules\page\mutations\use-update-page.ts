import { IPage } from '@/types/page';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface UpdatePageData {
  title?: string;
  image?: string;
  content?: string;
  slug?: string;
  showOnNav?: boolean;
  categoryId?: string;
}

interface UpdatePageVariables {
  id: string;
  data: UpdatePageData;
}

export function useUpdatePage() {
  const queryClient = useQueryClient();

  return useMutation<IApiResponse<IPage>, Error, UpdatePageVariables>({
    mutationFn: ({ id, data }: UpdatePageVariables) =>
      fetch(`/api/pages/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        credentials: 'include',
        body: JSON.stringify(data),
      }).then((res) => res.json()),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['pages'] });
      queryClient.invalidateQueries({ queryKey: ['page', id] });
    },
  });
}
