'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Pen, Trash } from 'lucide-react';

interface HotDeal {
    id: string;
    name: string;
    price: string;
}

const initialDeals: HotDeal[] = [
    { id: '1', name: 'LH MAIN WINDSHIELD', price: '$2,450' },
    { id: '2', name: 'TOP ASSEMBLY', price: '$1,850' },
    { id: '3', name: 'ENGINE', price: '$10,000' },
];

const HotDealsSection: React.FC = () => {
    const [deals, setDeals] = useState<HotDeal[]>(initialDeals);
    const router = useRouter();

    const handleDelete = (id: string) => {
        if (window.confirm('Are you sure you want to delete this deal?')) {
            setDeals((prev) => prev.filter((deal) => deal.id !== id));
        }
    };

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-3xl font-bold">Hot Deals List</h1>
                    <Link href="/home/<USER>/edit">
                        <Button>Edit All Deals</Button>
                    </Link>
                </div>

                {deals.length === 0 ? (
                    <p>No hot deals found.</p>
                ) : (
                    <table className="min-w-full text-left border-separate border-spacing-0">
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="border px-4 py-2">S.N.</th>
                                <th className="border px-4 py-2">Name</th>
                                <th className="border px-4 py-2">Price</th>
                                <th className="border px-4 py-2">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {deals.map((deal, idx) => (
                                <tr
                                    key={deal.id}
                                    className={`border-t ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                                        }`}
                                >
                                    <td className="border px-4 py-2">{idx + 1}</td>
                                    <td className="border px-4 py-2">{deal.name}</td>
                                    <td className="border px-4 py-2">{deal.price}</td>
                                    <td className="border px-4 py-2 flex gap-2">
                                        <Link href={`/home/<USER>/edit`}>
                                            <Button variant="outline" size="sm"
                                                className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors">
                                                <Pen className="w-4 h-4" />
                                            </Button>
                                        </Link>
                                        <button
                                            type="button"
                                            onClick={() => handleDelete(deal.id)}
                                            className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                        >
                                            <Trash className="w-4 h-4" />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
        </section>
    );
};

export default HotDealsSection;
