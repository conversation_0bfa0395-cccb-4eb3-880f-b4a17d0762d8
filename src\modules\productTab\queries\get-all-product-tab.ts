import { IProductTab } from '@/types/product';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetProductTab() {
  return useQuery<IApiResponse<IProductTab[]>, Error>({
    queryKey: ['product-tab'],
    queryFn: () =>
      fetch(`/api/product-tab`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
  });
}
