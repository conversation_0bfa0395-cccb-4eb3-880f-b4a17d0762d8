import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { toast } from 'sonner';
import { IHomeSupport } from '@/types/home';

export function useCreateSupport() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHomeSupport>, Error, IHomeSupport>({
    mutationFn: (data: IHomeSupport) =>
      fetch(`/api/home-support`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['support'] });
      toast.success('SUpport Created Sucessfully');
    },
    onError: () => {
      toast.error('Error Creating Support');
    },
  });
}
