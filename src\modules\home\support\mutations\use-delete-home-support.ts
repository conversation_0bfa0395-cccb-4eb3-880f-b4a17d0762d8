import { IHomeSupport } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function UseDeleteSupport() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHomeSupport>, Error, void>({
    mutationFn: () =>
      fetch(`/api/home-support`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Support'] });
    },
  });
}
