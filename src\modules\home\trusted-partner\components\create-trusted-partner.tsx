'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import FileUpload from '@/components/image/image-upload';

const TrustedPartnerCreatePage: React.FC = () => {
  const router = useRouter();

  const [description, setDescription] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [showUploader, setShowUploader] = useState(false);

  const handleSingleImageUpload = (url: string | string[] | null) => {
    let newImageUrl: string | null;

    if (Array.isArray(url)) {
      newImageUrl = url.length > 0 ? url[0] : null;
    } else {
      newImageUrl = url;
    }

    if (!newImageUrl) return;

    if (images.length >= 3) {
      alert('Maximum 3 images allowed');
      setShowUploader(false);
      return;
    }

    setImages([...images, newImageUrl]);
    setShowUploader(false);
  };

  const handleRemoveImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    setImages(newImages);
  };

  const handleSubmit = () => {
    if (!description.trim()) {
      alert('Description is required');
      return;
    }
    if (images.length === 0) {
      alert('Please upload at least one image');
      return;
    }
    alert('Trusted Partner created (dummy)');
    router.push('/home');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6 container mx-auto">
      <h2 className="text-2xl font-semibold mb-6">Create Trusted Partner</h2>

      <div className="mb-6">
        <label className="block font-medium mb-2">Description</label>
        <textarea
          className="w-full border rounded p-3 min-h-[120px]"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter description"
        />
      </div>

      <div className="mb-4 flex flex-wrap gap-4">
        {images.map((url, idx) => (
          <div key={idx} className="relative w-24 h-24 rounded border overflow-hidden">
            <button
              type="button"
              onClick={() => handleRemoveImage(idx)}
              className="absolute top-1 right-1 z-10 bg-black bg-opacity-60 rounded-full text-white w-6 h-6 flex items-center justify-center hover:bg-opacity-80"
              aria-label={`Remove image ${idx + 1}`}
            >
              &times;
            </button>
            <img
              src={url}
              alt={`Uploaded image ${idx + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>

      <div className="mb-6">
        {!showUploader && images.length < 3 && (
          <Button onClick={() => setShowUploader(true)}>Add Image</Button>
        )}

        {showUploader && (
          <FileUpload
            value={null}
            onChange={handleSingleImageUpload}
            multiple={false}
            accept="image/*"
            label="Upload Image"
          />
        )}
      </div>

      <div className="flex gap-4">
        <Button onClick={handleSubmit} className="bg-green-600 hover:bg-green-700 text-white">
          Create
        </Button>
        <Button onClick={() => router.push('/home')} variant="secondary">
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TrustedPartnerCreatePage;
